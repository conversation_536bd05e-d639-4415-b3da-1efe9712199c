"use client"

import { useState, useEffect } from "react"
import { useTranslation } from "react-i18next"
import { useContent } from "@/hooks/useContent"
import Link from "next/link"
import Image from "next/image"

interface Program {
  id: string
  title: string
  description: string
  image: string
  type: string
  city: string
  link: string
}

const ProgramShowcase = () => {
  const { t, i18n, ready } = useTranslation()
  const { getContent } = useContent()
  const [isClient, setIsClient] = useState(false)
  const [programs, setPrograms] = useState<Program[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    setIsClient(true)
  }, [])

  useEffect(() => {
    fetchPrograms()
  }, [i18n.language])

  const fetchPrograms = async () => {
    try {
      setLoading(true)
      const language = i18n.language || 'zh'
      const response = await fetch(`/api/homepage-showcase?language=${language}&limit=6`)

      if (response.ok) {
        const data = await response.json()
        if (data.programs && data.programs.length > 0) {
          setPrograms(data.programs)
        } else {
          // 如果没有首页展示数据，使用默认的项目数据
          await fetchFallbackPrograms(language)
        }
      } else {
        // API失败时使用默认数据
        await fetchFallbackPrograms(language)
      }
    } catch (error) {
      console.error('Failed to fetch showcase programs:', error)
      // 出错时使用默认数据
      await fetchFallbackPrograms(i18n.language || 'zh')
    } finally {
      setLoading(false)
    }
  }

  const fetchFallbackPrograms = async (language: string) => {
    try {
      // 获取一些中国项目和国际项目作为展示
      const [chinaResponse, internationalResponse] = await Promise.all([
        fetch(`/api/china-programs?language=${language}&limit=2`),
        fetch(`/api/programs?language=${language}&limit=4`)
      ])

      const fallbackPrograms: Program[] = []

      if (chinaResponse.ok) {
        const chinaData = await chinaResponse.json()
        chinaData.programs?.slice(0, 2).forEach((program: any) => {
          fallbackPrograms.push({
            id: program.id,
            title: program.title,
            description: program.description,
            image: program.featuredImage || '/placeholder-program.jpg',
            type: '游学中国',
            city: program.city?.name || program.city || '',
            link: `/study-china/${program.slug}`
          })
        })
      }

      if (internationalResponse.ok) {
        const internationalData = await internationalResponse.json()
        internationalData.programs?.slice(0, 4).forEach((program: any) => {
          fallbackPrograms.push({
            id: program.id,
            title: program.title,
            description: program.description,
            image: program.featuredImage || '/placeholder-program.jpg',
            type: '游学国际',
            city: program.city?.name || program.city || '',
            link: `/study-international/${program.slug}`
          })
        })
      }

      setPrograms(fallbackPrograms.slice(0, 6))
    } catch (error) {
      console.error('Failed to fetch fallback programs:', error)
      // 最后的备用硬编码数据
      setPrograms([
        {
          id: "fallback-1",
          title: "中国文化体验之旅",
          description: "深度体验中国传统文化与现代发展",
          image: "https://images.unsplash.com/photo-1506905925346-29e86c435425?q=80&w=2070&fit=crop",
          type: "游学中国",
          city: "北京",
          link: "/study-china",
        },
        {
          id: "fallback-2",
          title: "新加坡STEM探索营",
          description: "探索科技前沿，体验创新教育",
          image: "https://images.unsplash.com/photo-1581362072978-14998d01fdaa?q=80&w=2070&fit=crop",
          type: "游学国际",
          city: "新加坡",
          link: "/programs",
        },
        {
          id: "fallback-3",
          title: "AI与未来科技体验",
          description: "了解人工智能，探索未来科技",
          image: "https://images.unsplash.com/photo-1485827404703-89b55fcc595e?q=80&w=2070&fit=crop",
          type: "游学国际",
          city: "新加坡",
          link: "/programs",
        },
      ])
    }
  }

  const getText = (key: string, fallback: string) => {
    // 优先从数据库获取内容，如果没有则使用 i18n
    const dbContent = getContent(key)
    if (dbContent) return dbContent

    // During SSR or before i18n is ready, return fallback to prevent hydration mismatch
    if (!isClient || !ready) return fallback
    try {
      return t(key) || fallback
    } catch {
      return fallback
    }
  }

  if (loading) {
    return (
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              {getText("program_showcase_title", "精选项目展示")}
            </h2>
          </div>
          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {[1, 2, 3].map((i) => (
              <div key={i} className="bg-white rounded-lg overflow-hidden shadow-sm">
                <div className="h-48 bg-gray-200 animate-pulse"></div>
                <div className="p-6">
                  <div className="h-4 bg-gray-200 rounded animate-pulse mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    )
  }

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-6">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            {getText("program_showcase_title", "精选项目展示")}
          </h2>
          <Link href="/programs" className="text-blue-600 hover:text-blue-700 font-medium inline-flex items-center">
            {getText("program_showcase_link_text", "查看所有项目 →")}
          </Link>
        </div>

        <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {programs.map((program) => (
            <Link
              key={program.id}
              href={program.link}
              className="group bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-300"
            >
              <div className="relative h-48 overflow-hidden">
                <Image
                  src={program.image || "/placeholder-program.jpg"}
                  alt={program.title}
                  fill
                  className="object-cover transition-transform duration-300 group-hover:scale-105"
                />
              </div>
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">{program.title}</h3>
                <p className="text-sm text-gray-600 mb-3 line-clamp-2">{program.description}</p>
                <div className="flex justify-between items-center text-sm text-gray-500">
                  <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">{program.type}</span>
                  <span>{program.city}</span>
                </div>
              </div>
            </Link>
          ))}
        </div>

        {programs.length === 0 && !loading && (
          <div className="text-center py-12">
            <p className="text-gray-500">暂无项目数据</p>
          </div>
        )}
      </div>
    </section>
  )
}

export default ProgramShowcase
