const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testSimpleAPI() {
  try {
    console.log('🧪 测试简化的首页展示API逻辑...\n');

    const language = 'zh';
    const limit = 6;

    // 1. 获取展示数据
    console.log('1. 获取展示数据:');
    const showcases = await prisma.homepageShowcase.findMany({
      where: {
        isActive: true,
      },
      orderBy: [
        { position: 'asc' },
        { order: 'asc' },
        { createdAt: 'desc' }
      ],
      take: limit,
    });

    console.log(`   找到 ${showcases.length} 个展示项目`);

    // 2. 简化处理 - 只获取基本信息
    const showcasePrograms = [];
    
    for (const showcase of showcases) {
      console.log(`   处理位置 ${showcase.position}: ${showcase.programSlug} (${showcase.programType})`);
      
      let program = null;

      if (showcase.programType === 'china') {
        // 查询中国项目 - 不包含城市关联
        const chinaProgram = await prisma.chinaProgram.findFirst({
          where: {
            slug: showcase.programSlug,
            status: 'PUBLISHED',
          },
        });

        if (chinaProgram) {
          program = {
            id: chinaProgram.id,
            title: showcase.titleZh || chinaProgram.title,
            description: chinaProgram.description,
            image: chinaProgram.featuredImage || '/placeholder-program.jpg',
            type: showcase.programTypeZh || '游学中国',
            city: showcase.cityZh || '',
            link: `/study-china/${chinaProgram.slug}`,
          };
          console.log(`     ✅ 找到中国项目: ${chinaProgram.title}`);
        } else {
          console.log(`     ❌ 未找到中国项目`);
        }

      } else if (showcase.programType === 'international') {
        // 查询国际项目 - 不包含城市关联
        const internationalProgram = await prisma.internationalProgram.findFirst({
          where: {
            slug: showcase.programSlug,
            status: 'PUBLISHED',
          },
        });

        if (internationalProgram) {
          program = {
            id: internationalProgram.id,
            title: showcase.titleZh || internationalProgram.title,
            description: internationalProgram.description,
            image: internationalProgram.featuredImage || '/placeholder-program.jpg',
            type: showcase.programTypeZh || '游学国际',
            city: showcase.cityZh || '',
            link: `/study-international/${internationalProgram.slug}`,
          };
          console.log(`     ✅ 找到国际项目: ${internationalProgram.title}`);
        } else {
          console.log(`     ❌ 未找到国际项目`);
        }
      }

      if (program) {
        showcasePrograms.push({
          position: showcase.position,
          program,
        });
      }
    }

    // 3. 过滤和排序
    const validShowcases = showcasePrograms
      .filter(item => item.program !== null)
      .sort((a, b) => a.position - b.position)
      .map(item => item.program);

    console.log(`\n✅ 成功处理 ${validShowcases.length} 个展示项目:`);
    validShowcases.forEach((program, index) => {
      console.log(`   ${index + 1}. ${program.title} (${program.type})`);
    });

    // 4. 模拟API响应
    const response = {
      programs: validShowcases,
    };

    console.log(`\n📄 API响应预览:`);
    console.log(JSON.stringify(response, null, 2));

    console.log('\n✅ 测试完成!');

  } catch (error) {
    console.error('❌ 测试过程中出错:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testSimpleAPI();
