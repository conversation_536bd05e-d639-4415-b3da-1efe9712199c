async function testAPIs() {
  console.log('🧪 测试前端API接口...\n');

  const baseUrl = 'http://localhost:3000';

  try {
    // 1. 测试中国项目API
    console.log('1. 测试中国项目API:');
    const chinaResponse = await fetch(`${baseUrl}/api/china-programs?language=zh&status=PUBLISHED`);
    console.log(`   状态码: ${chinaResponse.status}`);
    
    if (chinaResponse.ok) {
      const chinaData = await chinaResponse.json();
      console.log(`   ✅ 成功获取 ${chinaData.programs?.length || 0} 个中国项目`);
      if (chinaData.programs && chinaData.programs.length > 0) {
        chinaData.programs.forEach((program, index) => {
          console.log(`   ${index + 1}. ${program.title}`);
        });
      }
    } else {
      const errorText = await chinaResponse.text();
      console.log(`   ❌ 失败: ${errorText}`);
    }
    console.log('');

    // 2. 测试国际项目API
    console.log('2. 测试国际项目API:');
    const programsResponse = await fetch(`${baseUrl}/api/programs?language=zh&status=PUBLISHED`);
    console.log(`   状态码: ${programsResponse.status}`);
    
    if (programsResponse.ok) {
      const programsData = await programsResponse.json();
      console.log(`   ✅ 成功获取 ${programsData.programs?.length || 0} 个国际项目`);
      if (programsData.programs && programsData.programs.length > 0) {
        programsData.programs.slice(0, 3).forEach((program, index) => {
          console.log(`   ${index + 1}. ${program.title}`);
        });
        if (programsData.programs.length > 3) {
          console.log(`   ... 还有 ${programsData.programs.length - 3} 个项目`);
        }
      }
    } else {
      const errorText = await programsResponse.text();
      console.log(`   ❌ 失败: ${errorText}`);
    }
    console.log('');

    // 3. 测试博客API
    console.log('3. 测试博客API:');
    const blogsResponse = await fetch(`${baseUrl}/api/blogs?language=zh&status=PUBLISHED`);
    console.log(`   状态码: ${blogsResponse.status}`);
    
    if (blogsResponse.ok) {
      const blogsData = await blogsResponse.json();
      console.log(`   ✅ 成功获取 ${blogsData.blogs?.length || 0} 篇博客`);
      if (blogsData.blogs && blogsData.blogs.length > 0) {
        blogsData.blogs.forEach((blog, index) => {
          console.log(`   ${index + 1}. ${blog.title}`);
        });
      }
    } else {
      const errorText = await blogsResponse.text();
      console.log(`   ❌ 失败: ${errorText}`);
    }
    console.log('');

    // 4. 测试共享字段API
    console.log('4. 测试共享字段API:');
    const sharedFieldsResponse = await fetch(`${baseUrl}/api/shared-fields`);
    console.log(`   状态码: ${sharedFieldsResponse.status}`);
    
    if (sharedFieldsResponse.ok) {
      const sharedFieldsData = await sharedFieldsResponse.json();
      console.log(`   ✅ 成功获取共享字段:`);
      console.log(`   - 项目类型: ${sharedFieldsData.programTypes?.length || 0} 个`);
      console.log(`   - 年级级别: ${sharedFieldsData.gradeLevels?.length || 0} 个`);
      console.log(`   - 国家: ${sharedFieldsData.countries?.length || 0} 个`);
      console.log(`   - 城市: ${sharedFieldsData.cities?.length || 0} 个`);
    } else {
      const errorText = await sharedFieldsResponse.text();
      console.log(`   ❌ 失败: ${errorText}`);
    }
    console.log('');

    // 5. 测试首页展示API
    console.log('5. 测试首页展示API:');
    const showcaseResponse = await fetch(`${baseUrl}/api/homepage-showcase?language=zh`);
    console.log(`   状态码: ${showcaseResponse.status}`);
    
    if (showcaseResponse.ok) {
      const showcaseData = await showcaseResponse.json();
      console.log(`   ✅ 成功获取 ${showcaseData.programs?.length || 0} 个首页展示项目`);
    } else {
      const errorText = await showcaseResponse.text();
      console.log(`   ❌ 失败: ${errorText}`);
    }

  } catch (error) {
    console.error('❌ 测试过程中出错:', error.message);
  }
}

testAPIs();
