"use client"

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

interface User {
  id: string
  email: string
  username: string
  role: string
  name?: string
}

export default function AdminDashboard() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    checkAuth()
  }, [])

  const checkAuth = async () => {
    try {
      const response = await fetch('/api/auth/me')
      if (response.ok) {
        const data = await response.json()
        setUser(data.user)
      } else {
        // Only redirect to login if authentication is not disabled
        if (process.env.NEXT_PUBLIC_DISABLE_AUTH !== 'true') {
          router.push('/admin/login')
        } else {
          console.log('🔓 Authentication disabled - using development mode')
          // Set a mock user for development
          setUser({
            id: 'dev-user-id',
            email: '<EMAIL>',
            username: 'developer',
            role: 'ADMIN',
            name: 'Development User'
          })
        }
      }
    } catch (error) {
      console.error('Auth check failed:', error)
      // Only redirect to login if authentication is not disabled
      if (process.env.NEXT_PUBLIC_DISABLE_AUTH !== 'true') {
        router.push('/admin/login')
      } else {
        console.log('🔓 Authentication disabled - using development mode')
        // Set a mock user for development
        setUser({
          id: 'dev-user-id',
          email: '<EMAIL>',
          username: 'developer',
          role: 'ADMIN',
          name: 'Development User'
        })
      }
    } finally {
      setLoading(false)
    }
  }

  const handleLogout = async () => {
    try {
      await fetch('/api/auth/logout', { method: 'POST' })
      router.push('/admin/login')
    } catch (error) {
      console.error('Logout failed:', error)
    }
  }

  const initializeSystem = async () => {
    try {
      const response = await fetch('/api/init', { method: 'POST' })
      if (response.ok) {
        alert('系统初始化成功！')
      } else {
        alert('初始化失败')
      }
    } catch (error) {
      console.error('Initialization failed:', error)
      alert('初始化失败')
    }
  }

  const syncContent = async () => {
    try {
      const response = await fetch('/api/admin/sync-content', { method: 'POST' })
      if (response.ok) {
        const data = await response.json()
        alert(`内容同步成功！同步了 ${data.syncedCount} 个内容项`)
      } else {
        alert('内容同步失败')
      }
    } catch (error) {
      console.error('Content sync failed:', error)
      alert('内容同步失败')
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div>Loading...</div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <h1 className="text-3xl font-bold text-gray-900">
              EdGoing 管理后台
            </h1>
            <div className="flex items-center space-x-4">
              <span className="text-gray-700">
                欢迎，{user.name || user.username}
              </span>
              <Button onClick={handleLogout} variant="outline">
                退出登录
              </Button>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">

            {/* System Status */}
            <Card>
              <CardHeader>
                <CardTitle>系统状态</CardTitle>
                <CardDescription>
                  当前系统信息
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <p><strong>用户角色：</strong> {user.role === 'ADMIN' ? '管理员' : user.role === 'EDITOR' ? '编辑者' : '查看者'}</p>
                  <p><strong>数据库：</strong> 已连接</p>
                  <Button onClick={initializeSystem} className="w-full mt-2">
                    初始化系统
                  </Button>
                  <Button onClick={syncContent} className="w-full mt-2" variant="outline">
                    同步网站内容
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Content Management */}
            <Card>
              <CardHeader>
                <CardTitle>内容管理</CardTitle>
                <CardDescription>
                  管理网站内容
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Button className="w-full" onClick={() => router.push('/admin/testimonials')}>
                    学员故事库
                  </Button>
                  <Button className="w-full bg-blue-600 hover:bg-blue-700" onClick={() => router.push('/admin/international-programs')}>
                    游学国际项目
                  </Button>
                  <Button className="w-full bg-red-600 hover:bg-red-700" onClick={() => router.push('/admin/china-programs')}>
                    游学中国项目
                  </Button>
                  <Button className="w-full bg-green-600 hover:bg-green-700" onClick={() => router.push('/admin/blogs')}>
                    博客库
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Media Management */}
            <Card>
              <CardHeader>
                <CardTitle>媒体管理</CardTitle>
                <CardDescription>
                  上传和管理媒体文件
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Button className="w-full" onClick={() => router.push('/admin/videos')}>
                    博客视频库
                  </Button>
                  <Button className="w-full" onClick={() => router.push('/admin/media/upload')}>
                    上传文件
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* User Management */}
            {user.role === 'ADMIN' && (
              <Card>
                <CardHeader>
                  <CardTitle>用户管理</CardTitle>
                  <CardDescription>
                    管理系统用户
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <Button className="w-full" onClick={() => router.push('/admin/users')}>
                      用户管理
                    </Button>
                    <Button className="w-full" onClick={() => router.push('/admin/users/create')}>
                      创建用户
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Settings */}
            <Card>
              <CardHeader>
                <CardTitle>网站设置</CardTitle>
                <CardDescription>
                  配置网站设置
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Button className="w-full" onClick={() => router.push('/admin/settings')}>
                    常规设置
                  </Button>
                  <Button className="w-full" onClick={() => router.push('/admin/content')}>
                    网站内容管理
                  </Button>
                  <Button className="w-full" onClick={() => router.push('/admin/content/sections')}>
                    页面板块管理
                  </Button>
                  <Button className="w-full bg-purple-600 hover:bg-purple-700" onClick={() => router.push('/admin/shared-fields')}>
                    共享字段管理
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Applications */}
            <Card>
              <CardHeader>
                <CardTitle>申请管理</CardTitle>
                <CardDescription>
                  管理项目申请
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Button className="w-full" onClick={() => router.push('/admin/applications')}>
                    查看申请
                  </Button>
                  <Button className="w-full" onClick={() => router.push('/admin/newsletters')}>
                    邮件订阅管理
                  </Button>
                </div>
              </CardContent>
            </Card>

          </div>
        </div>
      </main>
    </div>
  )
}
