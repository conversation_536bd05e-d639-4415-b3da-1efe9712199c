import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { requireEditor } from '@/lib/middleware'

export async function GET(request: NextRequest) {
  try {
    const authResult = await requireEditor(request)
    if (authResult instanceof NextResponse) {
      return authResult
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const language = searchParams.get('language') || 'zh'
    const country = searchParams.get('country')
    const city = searchParams.get('city')
    const type = searchParams.get('type')
    const gradeLevel = searchParams.get('gradeLevel')
    const status = searchParams.get('status')

    const where: any = {}

    // Build where conditions
    if (status) {
      where.status = status
    }

    if (country) {
      where.country = country
    }

    if (city) {
      where.city = {
        name: city
      }
    }

    if (type) {
      where.type = {
        contains: type
      }
    }

    if (gradeLevel) {
      where.gradeLevel = {
        contains: gradeLevel
      }
    }

    // Get programs with translations
    const [programs, total] = await Promise.all([
      prisma.internationalProgram.findMany({
        where,
        include: {
          author: {
            select: {
              name: true,
              username: true,
            }
          },
          city: {
            include: {
              country: true
            }
          },
          translations: {
            where: {
              language
            }
          },
          _count: {
            select: {
              applications: true,
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.internationalProgram.count({ where }),
    ])

    // Format response with translations
    const formattedPrograms = programs.map(program => {
      const translation = program.translations[0]
      return {
        id: program.id,
        title: translation?.title || program.title,
        slug: program.slug,
        description: translation?.description || program.description,
        duration: translation?.duration || program.duration,
        status: program.status,
        language: program.language,
        country: program.city?.country?.name || program.country,
        city: program.city?.name,
        cityId: program.cityId,
        deadline: program.deadline,
        featuredImage: program.featuredImage,
        gallery: program.gallery ? JSON.parse(program.gallery) : [],
        highlights: translation?.highlights ? JSON.parse(translation.highlights) : (program.highlights ? JSON.parse(program.highlights) : []),
        academics: translation?.academics ? JSON.parse(translation.academics) : (program.academics ? JSON.parse(program.academics) : []),
        itinerary: translation?.itinerary ? JSON.parse(translation.itinerary) : (program.itinerary ? JSON.parse(program.itinerary) : []),
        requirements: translation?.requirements ? JSON.parse(translation.requirements) : (program.requirements ? JSON.parse(program.requirements) : []),
        type: program.type ? JSON.parse(program.type) : [],
        gradeLevel: program.gradeLevel ? JSON.parse(program.gradeLevel) : [],
        sessions: translation?.sessions ? JSON.parse(translation.sessions) : (program.sessions ? JSON.parse(program.sessions) : []),
        publishedAt: program.publishedAt,
        createdAt: program.createdAt,
        updatedAt: program.updatedAt,
        author: program.author,
        _count: {
          applications: program._count.applications
        },
      }
    })

    return NextResponse.json({
      programs: formattedPrograms,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Get international programs error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const authResult = await requireEditor(request)
    if (authResult instanceof NextResponse) {
      return authResult
    }

    const body = await request.json()
    const {
      title,
      slug,
      description,
      duration,
      status = 'DRAFT',
      language = 'zh',
      country,
      cityId,
      deadline,
      featuredImage,
      gallery = [],
      highlights = [],
      academics = [],
      itinerary = [],
      requirements = [],
      type = [],
      gradeLevel = [],
      sessions = [],
      translations = {}
    } = body

    // Create the program (including required fields)
    const program = await prisma.internationalProgram.create({
      data: {
        title,
        slug,
        description,
        duration,
        status,
        language,
        country,
        cityId,
        deadline: deadline ? new Date(deadline) : null,
        featuredImage,
        gallery: JSON.stringify(gallery),
        highlights: JSON.stringify(highlights),
        academics: JSON.stringify(academics),
        itinerary: JSON.stringify(itinerary),
        requirements: JSON.stringify(requirements),
        sessions: JSON.stringify(sessions),
        type: JSON.stringify(type),
        gradeLevel: JSON.stringify(gradeLevel),
        publishedAt: status === 'PUBLISHED' ? new Date() : null,
        authorId: authResult.user.id,
      },
      include: {
        city: {
          include: {
            country: true
          }
        },
        author: {
          select: {
            name: true,
            username: true,
          }
        }
      }
    })

    // Create translation for the current language
    await prisma.internationalProgramTranslation.create({
      data: {
        programId: program.id,
        language: language,
        title,
        description,
        duration,
        highlights: JSON.stringify(highlights),
        academics: JSON.stringify(academics),
        itinerary: JSON.stringify(itinerary),
        requirements: JSON.stringify(requirements),
        sessions: JSON.stringify(sessions),
        materials: JSON.stringify([]),
      }
    })

    // Create additional translations if provided
    if (Object.keys(translations).length > 0) {
      const translationData = Object.entries(translations)
        .filter(([lang]) => lang !== language) // Don't duplicate the current language
        .map(([lang, data]: [string, any]) => ({
          programId: program.id,
          language: lang,
          title: data.title || title,
          description: data.description || description,
          duration: data.duration || duration,
          highlights: JSON.stringify(data.highlights || highlights),
          academics: JSON.stringify(data.academics || academics),
          itinerary: JSON.stringify(data.itinerary || itinerary),
          requirements: JSON.stringify(data.requirements || requirements),
          sessions: JSON.stringify(data.sessions || sessions),
          materials: JSON.stringify(data.materials || []),
        }))

      if (translationData.length > 0) {
        await prisma.internationalProgramTranslation.createMany({
          data: translationData
        })
      }
    }

    return NextResponse.json({ program }, { status: 201 })
  } catch (error) {
    console.error('Create international program error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
