import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function GET(request: NextRequest) {
  try {
    console.log('Homepage showcase API called')
    const { searchParams } = new URL(request.url)
    const language = searchParams.get('language') || 'zh'
    const limit = parseInt(searchParams.get('limit') || '6')

    console.log('Parameters:', { language, limit })

    // Get active homepage showcase items
    console.log('Fetching showcases...')
    const showcases = await prisma.homepageShowcase.findMany({
      where: {
        isActive: true,
      },
      orderBy: [
        { position: 'asc' },
        { order: 'asc' },
        { createdAt: 'desc' }
      ],
      take: limit,
    })

    console.log('Found showcases:', showcases.length)

    // Fetch program details for each showcase item
    const showcasePrograms = await Promise.all(
      showcases.map(async (showcase) => {
        let program = null

        if (showcase.programType === 'china') {
          // Fetch China program
          const chinaProgram = await prisma.chinaProgram.findFirst({
            where: {
              slug: showcase.programSlug,
              status: 'PUBLISHED',
            },
          })

          if (chinaProgram) {
            program = {
              id: chinaProgram.id,
              title: language === 'zh'
                ? (showcase.titleZh || chinaProgram.title)
                : (showcase.titleEn || chinaProgram.title),
              description: chinaProgram.description,
              image: chinaProgram.featuredImage || '/placeholder-program.jpg',
              type: language === 'zh'
                ? (showcase.programTypeZh || '游学中国')
                : (showcase.programTypeEn || 'Study China'),
              city: language === 'zh'
                ? (showcase.cityZh || '')
                : (showcase.cityEn || ''),
              link: `/study-china/${chinaProgram.slug}`,
            }
          }
        } else if (showcase.programType === 'international') {
          // Fetch International program
          const internationalProgram = await prisma.internationalProgram.findFirst({
            where: {
              slug: showcase.programSlug,
              status: 'PUBLISHED',
            },
          })

          if (internationalProgram) {
            program = {
              id: internationalProgram.id,
              title: language === 'zh'
                ? (showcase.titleZh || internationalProgram.title)
                : (showcase.titleEn || internationalProgram.title),
              description: internationalProgram.description,
              image: internationalProgram.featuredImage || '/placeholder-program.jpg',
              type: language === 'zh'
                ? (showcase.programTypeZh || '游学国际')
                : (showcase.programTypeEn || 'Study International'),
              city: language === 'zh'
                ? (showcase.cityZh || '')
                : (showcase.cityEn || ''),
              link: `/study-international/${internationalProgram.slug}`,
            }
          }
        }

        return {
          position: showcase.position,
          program,
        }
      })
    )

    // Filter out showcases where program was not found and sort by position
    const validShowcases = showcasePrograms
      .filter(item => item.program !== null)
      .sort((a, b) => a.position - b.position)
      .map(item => item.program)

    return NextResponse.json({
      programs: validShowcases,
    })
  } catch (error) {
    console.error('Get homepage showcase error:', error)
    console.error('Error stack:', error instanceof Error ? error.stack : 'No stack trace')
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}
