"use client"

import { Suspense } from "react"
import PageHero from "@/components/PageHero"
import ContactForm from "@/components/ContactForm"
import Footer from "@/components/Footer"
import { useTranslation } from "react-i18next"
import { useContent } from "@/hooks/useContent"

export default function ContactPage() {
  const { t, ready } = useTranslation()
  const { getContent } = useContent()

  const getText = (key: string, fallback: string) => {
    // 优先从数据库获取内容，如果没有则使用 i18n
    const dbContent = getContent(key)
    if (dbContent) return dbContent

    if (!ready) return fallback
    try {
      return t(key) || fallback
    } catch {
      return fallback
    }
  }

  return (
    <div className="min-h-screen flex flex-col">
      <PageHero
        title={getText("contact.hero.title", "开始您的规划")}
        description={getText("contact.hero.description", "准备开始您的教育之旅？让我们帮助您创造完美的留学体验。")}
        backgroundImage="/placeholder.svg?height=800&width=1920"
      />
      <div className="flex-grow bg-white py-16">
        <div className="container mx-auto px-4">
          <Suspense fallback={<div className="flex justify-center items-center py-8">Loading...</div>}>
            <ContactForm />
          </Suspense>
        </div>
      </div>
      <Footer />
    </div>
  )
}
