const { PrismaClient } = require('@prisma/client');
const path = require('path');
const fs = require('fs');

async function verifyDatabase() {
  console.log('🔍 验证数据库连接...\n');

  // 1. 检查环境变量
  console.log('1. 环境变量检查:');
  console.log(`   DATABASE_URL: ${process.env.DATABASE_URL || '未设置'}`);
  
  // 2. 检查数据库文件
  console.log('\n2. 数据库文件检查:');
  const dbPath = path.join(__dirname, 'prisma', 'dev.db');
  console.log(`   数据库文件路径: ${dbPath}`);
  
  if (fs.existsSync(dbPath)) {
    const stats = fs.statSync(dbPath);
    console.log(`   ✅ 文件存在`);
    console.log(`   文件大小: ${(stats.size / 1024).toFixed(2)} KB`);
    console.log(`   最后修改: ${stats.mtime.toLocaleString()}`);
  } else {
    console.log(`   ❌ 文件不存在`);
    return;
  }

  // 3. 测试 Prisma 连接
  console.log('\n3. Prisma 连接测试:');
  const prisma = new PrismaClient();
  
  try {
    // 测试连接
    await prisma.$connect();
    console.log('   ✅ Prisma 连接成功');

    // 测试查询
    const userCount = await prisma.user.count();
    const chinaCount = await prisma.chinaProgram.count();
    const intlCount = await prisma.internationalProgram.count();
    const blogCount = await prisma.blog.count();
    
    console.log(`   用户数量: ${userCount}`);
    console.log(`   中国项目数量: ${chinaCount}`);
    console.log(`   国际项目数量: ${intlCount}`);
    console.log(`   博客数量: ${blogCount}`);

  } catch (error) {
    console.log(`   ❌ Prisma 连接失败: ${error.message}`);
  } finally {
    await prisma.$disconnect();
  }

  // 4. 检查 Prisma Studio 连接
  console.log('\n4. Prisma Studio 信息:');
  console.log('   URL: http://localhost:5556');
  console.log('   连接到同一个数据库文件: dev.db');

  // 5. 提供确保连接一致性的建议
  console.log('\n📋 确保数据库连接一致性的建议:');
  console.log('   1. 始终在 /Users/<USER>/Downloads/EdGoing/website 目录下工作');
  console.log('   2. 确保 .env 文件中的 DATABASE_URL="file:./dev.db"');
  console.log('   3. 运行 Prisma 命令时在 website 目录下');
  console.log('   4. 启动 Next.js 开发服务器时在 website 目录下');
  console.log('   5. 启动 Prisma Studio 时在 website 目录下');

  console.log('\n✅ 数据库验证完成!');
}

// 运行验证
verifyDatabase().catch(console.error);
