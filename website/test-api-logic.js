const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testAPILogic() {
  try {
    console.log('🧪 测试首页展示API逻辑...\n');

    const language = 'zh';
    const limit = 6;

    // 1. 获取展示数据
    console.log('1. 获取展示数据:');
    const showcases = await prisma.homepageShowcase.findMany({
      where: {
        isActive: true,
      },
      orderBy: [
        { position: 'asc' },
        { order: 'asc' },
        { createdAt: 'desc' }
      ],
      take: limit,
    });

    console.log(`   找到 ${showcases.length} 个展示项目`);
    showcases.forEach(showcase => {
      console.log(`   - 位置 ${showcase.position}: ${showcase.programSlug} (${showcase.programType})`);
    });
    console.log('');

    // 2. 逐个处理展示项目
    console.log('2. 处理展示项目:');
    for (const showcase of showcases) {
      console.log(`   处理位置 ${showcase.position}: ${showcase.programSlug} (${showcase.programType})`);

      try {
        if (showcase.programType === 'china') {
          // 查询中国项目
          const chinaProgram = await prisma.chinaProgram.findFirst({
            where: {
              slug: showcase.programSlug,
              status: 'PUBLISHED',
            },
            include: {
              city: {
                select: {
                  name: true,
                  nameEn: true,
                },
              },
              translations: true,
            },
          });

          if (chinaProgram) {
            console.log(`     ✅ 找到中国项目: ${chinaProgram.title}`);
            console.log(`     城市数据: ${JSON.stringify(chinaProgram.city)}`);
            console.log(`     翻译数量: ${chinaProgram.translations.length}`);
          } else {
            console.log(`     ❌ 未找到中国项目`);
          }

        } else if (showcase.programType === 'international') {
          // 查询国际项目
          const internationalProgram = await prisma.internationalProgram.findFirst({
            where: {
              slug: showcase.programSlug,
              status: 'PUBLISHED',
            },
            include: {
              city: {
                select: {
                  name: true,
                  nameEn: true,
                },
              },
              translations: true,
            },
          });

          if (internationalProgram) {
            console.log(`     ✅ 找到国际项目: ${internationalProgram.title}`);
            console.log(`     城市数据: ${JSON.stringify(internationalProgram.city)}`);
            console.log(`     翻译数量: ${internationalProgram.translations.length}`);
          } else {
            console.log(`     ❌ 未找到国际项目`);
          }
        }
      } catch (error) {
        console.log(`     ❌ 处理出错: ${error.message}`);
      }
      console.log('');
    }

    console.log('✅ 测试完成!');

  } catch (error) {
    console.error('❌ 测试过程中出错:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testAPILogic();
