# EdGoing CMS 管理系统使用指南

## 系统概述

EdGoing CMS 是一个完整的内容管理系统，支持双语网站内容管理、程序管理、用户管理等功能。

## 功能特性

### 1. 双语支持
- 完整的中英文双语支持
- 语言切换功能
- 多语言内容管理

### 2. 内容管理
- 动态网站内容管理
- 支持通过后台修改网站所有文本
- 实时内容更新

### 3. 程序管理
- 游学项目管理
- 项目创建、编辑、发布
- 申请管理

### 4. 用户管理
- 多角色用户系统（管理员、编辑者、查看者）
- 安全的认证系统
- 会话管理

## 快速开始

### 1. 访问管理后台
访问：http://localhost:3000/admin/login

默认管理员账户：
- 邮箱：<EMAIL>
- 密码：admin123

### 2. 初始化系统
首次登录后，点击"Initialize System"按钮来初始化系统数据。

### 3. 管理网站内容
1. 在管理仪表板中点击"General Settings"
2. 选择语言（中文/英文）
3. 编辑网站内容
4. 点击"Save Changes"保存

### 4. 管理程序
1. 点击"Manage Programs"
2. 点击"Create Program"创建新项目
3. 填写项目信息
4. 设置状态为"Published"发布

## 数据库结构

### 主要表格
- `users` - 用户管理
- `china_programs` - 中国游学项目
- `international_programs` - 国际游学项目
- `china_program_translations` / `international_program_translations` - 项目多语言内容
- `blogs` - 博客文章管理
- `blog_translations` - 博客多语言内容
- `testimonials` - 学员故事
- `testimonial_translations` - 学员故事多语言内容
- `videos` - 视频库管理
- `settings` - 网站设置
- `setting_translations` - 设置多语言内容
- `applications` - 项目申请
- `newsletters` - 邮件订阅
- `media` - 媒体文件管理

### 已清理的表格
以下表格已被删除（未使用）：
- `posts`, `post_translations` - 被 `blogs` 替代
- `categories`, `category_translations` - 未使用
- `tags`, `tag_translations` - 未使用
- `post_categories`, `post_tags` - 关联表，未使用

## API 接口

### 认证接口
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出
- `GET /api/auth/me` - 获取当前用户信息

### 管理接口
- `GET /api/admin/programs` - 获取项目列表
- `POST /api/admin/programs` - 创建新项目
- `GET /api/admin/content` - 获取网站内容
- `PUT /api/admin/content` - 更新网站内容

### 公共接口
- `GET /api/content` - 获取网站内容（前端使用）

## 开发说明

### 技术栈
- Next.js 15
- React 19
- TypeScript
- Prisma ORM
- SQLite 数据库
- Tailwind CSS
- React i18next

### 项目结构
```
website/
├── app/                    # Next.js App Router
│   ├── admin/             # 管理后台页面
│   ├── api/               # API 路由
│   └── ...                # 其他页面
├── components/            # React 组件
├── hooks/                 # 自定义 Hooks
├── lib/                   # 工具库
├── prisma/               # 数据库模式
└── ...
```

### 添加新的内容字段
1. 在 `/api/admin/content/route.ts` 中添加新的内容键
2. 在内容管理页面中添加对应的表单字段
3. 在前端组件中使用 `useContent` hook 获取内容

### 扩展功能
- 媒体文件上传
- 更多内容类型（富文本编辑器）
- 用户权限细化
- 数据导入导出
- 多站点管理

## 部署说明

### 生产环境部署
1. 设置环境变量
2. 配置生产数据库
3. 运行数据库迁移
4. 构建和部署应用

### 环境变量
```
DATABASE_URL="your-database-url"
JWT_SECRET="your-jwt-secret"
NEXTAUTH_SECRET="your-nextauth-secret"
```

## 安全注意事项

1. 修改默认管理员密码
2. 使用强密码策略
3. 定期备份数据库
4. 启用 HTTPS
5. 限制管理后台访问

## 支持与维护

如有问题或需要技术支持，请联系开发团队。

定期更新系统以获得最新功能和安全补丁。
