const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testHomepageShowcase() {
  try {
    console.log('🧪 测试首页展示功能...\n');

    // 1. 检查 HomepageShowcase 表是否存在
    console.log('1. 检查 HomepageShowcase 表:');
    try {
      const showcases = await prisma.homepageShowcase.findMany();
      console.log(`   ✅ 表存在，当前有 ${showcases.length} 条记录`);
      
      if (showcases.length > 0) {
        showcases.forEach(showcase => {
          console.log(`   - 位置 ${showcase.position}: ${showcase.programSlug} (${showcase.programType})`);
        });
      }
    } catch (error) {
      console.log(`   ❌ 表不存在或查询失败: ${error.message}`);
      return;
    }
    console.log('');

    // 2. 创建一些测试数据
    console.log('2. 创建测试数据:');
    try {
      // 获取管理员用户ID
      const adminUser = await prisma.user.findFirst({
        where: { role: 'ADMIN' }
      });

      if (!adminUser) {
        console.log('   ❌ 找不到管理员用户');
        return;
      }

      // 获取一些项目数据
      const chinaPrograms = await prisma.chinaProgram.findMany({
        where: { status: 'PUBLISHED' },
        take: 2
      });

      const internationalPrograms = await prisma.internationalProgram.findMany({
        where: { status: 'PUBLISHED' },
        take: 2
      });

      console.log(`   找到 ${chinaPrograms.length} 个中国项目，${internationalPrograms.length} 个国际项目`);

      // 清除现有的展示数据
      await prisma.homepageShowcase.deleteMany();

      // 创建新的展示数据
      const showcaseData = [];

      if (chinaPrograms.length > 0) {
        showcaseData.push({
          position: 1,
          programType: 'china',
          programSlug: chinaPrograms[0].slug,
          titleZh: chinaPrograms[0].title,
          titleEn: chinaPrograms[0].title,
          programTypeZh: '游学中国',
          programTypeEn: 'Study China',
          isActive: true,
          authorId: adminUser.id
        });

        if (chinaPrograms.length > 1) {
          showcaseData.push({
            position: 2,
            programType: 'china',
            programSlug: chinaPrograms[1].slug,
            titleZh: chinaPrograms[1].title,
            titleEn: chinaPrograms[1].title,
            programTypeZh: '游学中国',
            programTypeEn: 'Study China',
            isActive: true,
            authorId: adminUser.id
          });
        }
      }

      if (internationalPrograms.length > 0) {
        showcaseData.push({
          position: 3,
          programType: 'international',
          programSlug: internationalPrograms[0].slug,
          titleZh: internationalPrograms[0].title,
          titleEn: internationalPrograms[0].title,
          programTypeZh: '游学国际',
          programTypeEn: 'Study International',
          isActive: true,
          authorId: adminUser.id
        });

        if (internationalPrograms.length > 1) {
          showcaseData.push({
            position: 4,
            programType: 'international',
            programSlug: internationalPrograms[1].slug,
            titleZh: internationalPrograms[1].title,
            titleEn: internationalPrograms[1].title,
            programTypeZh: '游学国际',
            programTypeEn: 'Study International',
            isActive: true,
            authorId: adminUser.id
          });
        }
      }

      // 批量创建展示数据
      for (const data of showcaseData) {
        await prisma.homepageShowcase.create({ data });
      }

      console.log(`   ✅ 成功创建 ${showcaseData.length} 条展示数据`);
    } catch (error) {
      console.log(`   ❌ 创建测试数据失败: ${error.message}`);
    }
    console.log('');

    // 3. 再次查询验证
    console.log('3. 验证创建的数据:');
    try {
      const showcases = await prisma.homepageShowcase.findMany({
        orderBy: { position: 'asc' }
      });
      
      console.log(`   ✅ 现在有 ${showcases.length} 条展示数据:`);
      showcases.forEach(showcase => {
        console.log(`   - 位置 ${showcase.position}: ${showcase.titleZh} (${showcase.programType})`);
      });
    } catch (error) {
      console.log(`   ❌ 验证失败: ${error.message}`);
    }

    console.log('\n✅ 测试完成!');

  } catch (error) {
    console.error('❌ 测试过程中出错:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testHomepageShowcase();
