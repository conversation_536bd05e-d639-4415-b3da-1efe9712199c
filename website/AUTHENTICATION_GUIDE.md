# 认证系统管理指南

## 当前状态：认证已禁用 🔓

为了避免开发过程中的认证问题导致数据丢失，当前系统已临时禁用认证功能。

## 开发模式配置

### 环境变量设置
在 `.env` 文件中：
```
DISABLE_AUTH="true"
```

### 模拟用户信息
开发模式下使用的模拟管理员用户：
- **用户名**: developer
- **邮箱**: <EMAIL>
- **角色**: ADMIN
- **姓名**: Development User

## 如何重新启用认证（上线前必做）

### 步骤 1: 修改环境变量
将 `.env` 文件中的 `DISABLE_AUTH` 设置为 `false` 或删除该行：
```
# DISABLE_AUTH="true"  # 注释掉或删除这行
```

### 步骤 2: 重启开发服务器
```bash
npm run dev
```

### 步骤 3: 创建管理员账户
重新启用认证后，您需要创建真正的管理员账户：

1. 访问 `/admin/login` 页面
2. 使用默认管理员账户登录：
   - **邮箱**: <EMAIL>
   - **密码**: admin123

3. 登录后立即修改密码和邮箱

### 步骤 4: 验证认证功能
- 确保登录/登出功能正常
- 确保未登录用户无法访问管理后台
- 确保API路由需要认证才能访问

## 生产环境部署注意事项

### 环境变量配置
生产环境的 `.env` 文件中：
```
# 数据库配置
DATABASE_URL="your-production-database-url"

# JWT密钥（必须更改）
JWT_SECRET="your-super-secure-production-jwt-secret"

# Next.js配置
NEXTAUTH_SECRET="your-production-nextauth-secret"
NEXTAUTH_URL="https://your-domain.com"

# 确保认证启用（不要包含 DISABLE_AUTH 变量）
```

### 安全检查清单
- [ ] 更改默认管理员密码
- [ ] 更新 JWT_SECRET 为强密码
- [ ] 确保 DISABLE_AUTH 未设置或为 false
- [ ] 测试所有认证功能
- [ ] 验证API路由安全性

## 受影响的文件

以下文件已被修改以支持开发模式：

### 环境配置
- `website/.env` - 添加了 DISABLE_AUTH 变量
- `website/next.config.mjs` - 添加了客户端环境变量

### 认证相关
- `website/lib/middleware.ts` - 添加了开发模式绕过逻辑
- `website/app/api/auth/me/route.ts` - 添加了模拟用户返回
- `website/lib/auth-utils.ts` - 新增的认证工具函数

### 管理页面
- `website/app/admin/page.tsx` - 修改了认证检查逻辑
- `website/app/admin/international-programs/page.tsx` - 修改了认证检查逻辑
- `website/app/admin/china-programs/page.tsx` - 修改了认证检查逻辑

## 恢复认证的快速方法

如果您想快速恢复认证功能进行测试：

1. 临时修改 `.env` 文件：
   ```
   DISABLE_AUTH="false"
   ```

2. 重启服务器：
   ```bash
   npm run dev
   ```

3. 测试完成后，如需继续开发，可以重新设置为 `"true"`

## 注意事项

⚠️ **重要提醒**：
- 生产环境部署前必须启用认证
- 定期备份数据库以防数据丢失
- 开发模式下的模拟用户仅用于开发，不会保存到数据库
- 所有API调用在开发模式下都会使用模拟用户身份

## 技术实现说明

开发模式通过以下方式绕过认证：
1. 环境变量 `DISABLE_AUTH="true"` 控制开关
2. 中间件检查环境变量，如果禁用则返回模拟用户
3. 前端页面检查环境变量，如果禁用则使用模拟用户
4. API路由通过中间件自动处理，无需额外修改

这种实现方式确保了：
- 认证代码完全保留，随时可以恢复
- 开发体验流畅，无需登录
- 生产环境安全，只需修改环境变量
