const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function checkFieldConsistency() {
  console.log('🔍 检查前端、CMS和数据库模型的字段一致性...\n')

  try {
    // 1. 检查数据库模型字段
    console.log('📊 数据库模型字段分析:')
    console.log('=' .repeat(50))

    // 检查中国项目模型
    console.log('\n🇨🇳 ChinaProgram 模型字段:')
    const chinaProgram = await prisma.chinaProgram.findFirst()
    if (chinaProgram) {
      const chinaFields = Object.keys(chinaProgram)
      console.log('   数据库字段:', chinaFields.join(', '))

      // 检查JSON字段
      const jsonFields = ['gallery', 'highlights', 'academics', 'itinerary', 'requirements', 'type', 'gradeLevel', 'sessions']
      console.log('   JSON字段:', jsonFields.join(', '))

      // 检查实际数据
      jsonFields.forEach(field => {
        if (chinaProgram[field]) {
          try {
            const parsed = JSON.parse(chinaProgram[field])
            console.log(`   ${field}: ${Array.isArray(parsed) ? `数组(${parsed.length}项)` : typeof parsed}`)
          } catch (e) {
            console.log(`   ${field}: 解析错误 - ${chinaProgram[field]}`)
          }
        } else {
          console.log(`   ${field}: 空值`)
        }
      })
    } else {
      console.log('   ❌ 没有找到中国项目数据')
    }

    // 检查国际项目模型
    console.log('\n🌍 InternationalProgram 模型字段:')
    const intlProgram = await prisma.internationalProgram.findFirst()
    if (intlProgram) {
      const intlFields = Object.keys(intlProgram)
      console.log('   数据库字段:', intlFields.join(', '))
    } else {
      console.log('   ❌ 没有找到国际项目数据')
    }

    // 检查博客模型
    console.log('\n📝 Blog 模型字段:')
    const blog = await prisma.blog.findFirst()
    if (blog) {
      const blogFields = Object.keys(blog)
      console.log('   数据库字段:', blogFields.join(', '))
    } else {
      console.log('   ❌ 没有找到博客数据')
    }

    // 检查学员故事模型
    console.log('\n💬 Testimonial 模型字段:')
    const testimonial = await prisma.testimonial.findFirst()
    if (testimonial) {
      const testimonialFields = Object.keys(testimonial)
      console.log('   数据库字段:', testimonialFields.join(', '))
    } else {
      console.log('   ❌ 没有找到学员故事数据')
    }

    // 检查共享字段
    console.log('\n🔗 共享字段数据:')
    const [programTypes, gradeLevels, countries, cities] = await Promise.all([
      prisma.programType.findMany({ take: 3 }),
      prisma.gradeLevel.findMany({ take: 3 }),
      prisma.country.findMany({ take: 3 }),
      prisma.city.findMany({ take: 3 })
    ])

    console.log(`   项目类型: ${programTypes.length}个 (示例: ${programTypes.map(p => p.name).join(', ')})`)
    console.log(`   年级级别: ${gradeLevels.length}个 (示例: ${gradeLevels.map(g => g.name).join(', ')})`)
    console.log(`   国家: ${countries.length}个 (示例: ${countries.map(c => c.name).join(', ')})`)
    console.log(`   城市: ${cities.length}个 (示例: ${cities.map(c => c.name).join(', ')})`)

    // 2. 分析字段一致性问题
    console.log('\n\n🔍 字段一致性分析:')
    console.log('=' .repeat(50))

    // 中国项目字段一致性
    console.log('\n🇨🇳 中国项目字段一致性:')
    const chinaFormFields = [
      'title', 'slug', 'description', 'country', 'cityId', 'duration',
      'deadline', 'featuredImage', 'gallery', 'highlights', 'academics',
      'itinerary', 'requirements', 'type', 'gradeLevel', 'sessions', 'status'
    ]

    const chinaDbFields = chinaProgram ? Object.keys(chinaProgram) : []
    const chinaMissingInDb = chinaFormFields.filter(field => !chinaDbFields.includes(field))
    const chinaExtraInDb = chinaDbFields.filter(field => !chinaFormFields.includes(field) && !['id', 'createdAt', 'updatedAt', 'authorId', 'publishedAt', 'language'].includes(field))

    console.log(`   ✅ CMS表单字段: ${chinaFormFields.join(', ')}`)
    console.log(`   ✅ 数据库字段: ${chinaDbFields.join(', ')}`)
    if (chinaMissingInDb.length > 0) {
      console.log(`   ⚠️  CMS有但数据库缺少: ${chinaMissingInDb.join(', ')}`)
    }
    if (chinaExtraInDb.length > 0) {
      console.log(`   ⚠️  数据库有但CMS未使用: ${chinaExtraInDb.join(', ')}`)
    }

    // 国际项目字段一致性
    console.log('\n🌍 国际项目字段一致性:')
    const intlFormFields = [
      'title', 'slug', 'description', 'country', 'cityId', 'duration',
      'deadline', 'featuredImage', 'gallery', 'highlights', 'academics',
      'itinerary', 'requirements', 'type', 'gradeLevel', 'sessions', 'status'
    ]

    const intlDbFields = intlProgram ? Object.keys(intlProgram) : []
    const intlMissingInDb = intlFormFields.filter(field => !intlDbFields.includes(field))
    const intlExtraInDb = intlDbFields.filter(field => !intlFormFields.includes(field) && !['id', 'createdAt', 'updatedAt', 'authorId', 'publishedAt', 'language'].includes(field))

    console.log(`   ✅ CMS表单字段: ${intlFormFields.join(', ')}`)
    console.log(`   ✅ 数据库字段: ${intlDbFields.join(', ')}`)
    if (intlMissingInDb.length > 0) {
      console.log(`   ⚠️  CMS有但数据库缺少: ${intlMissingInDb.join(', ')}`)
    }
    if (intlExtraInDb.length > 0) {
      console.log(`   ⚠️  数据库有但CMS未使用: ${intlExtraInDb.join(', ')}`)
    }

    // 3. 检查关键不匹配
    console.log('\n\n✅ 字段一致性状态:')
    console.log('=' .repeat(50))

    if (chinaMissingInDb.length === 0 && chinaExtraInDb.length === 0 && intlMissingInDb.length === 0 && intlExtraInDb.length === 0) {
      console.log('\n🎉 所有字段完全匹配!')
      console.log('   ✅ 中国项目: CMS表单与数据库字段完全一致')
      console.log('   ✅ 国际项目: CMS表单与数据库字段完全一致')
      console.log('   ✅ 城市字段: 统一使用 cityId 关联到 City 表')
      console.log('   ✅ JSON字段: 正确序列化和反序列化')
    } else {
      console.log('\n🏙️ 发现的不匹配:')
      if (chinaMissingInDb.length > 0 || chinaExtraInDb.length > 0) {
        console.log('   ⚠️  中国项目存在字段不匹配')
      }
      if (intlMissingInDb.length > 0 || intlExtraInDb.length > 0) {
        console.log('   ⚠️  国际项目存在字段不匹配')
      }
    }

    console.log('\n✅ 检查完成!')

  } catch (error) {
    console.error('❌ 检查过程中出错:', error)
  } finally {
    await prisma.$disconnect()
  }
}

checkFieldConsistency()
