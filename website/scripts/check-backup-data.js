const { PrismaClient } = require('@prisma/client')
const path = require('path')

// 创建一个指向备份数据库的 Prisma 客户端
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: 'file:../backups/full-backup-2025-06-02T07-38-19-140Z/database/dev.db'
    }
  }
})

async function checkBackupData() {
  console.log('🔍 检查备份数据库内容 (2025-06-02T07-38-19-140Z)...\n')

  try {
    // 检查用户
    const users = await prisma.user.count()
    console.log(`👥 用户数量: ${users}`)

    // 检查中国项目
    const chinaPrograms = await prisma.chinaProgram.count()
    console.log(`🇨🇳 中国项目数量: ${chinaPrograms}`)

    // 检查国际项目
    const internationalPrograms = await prisma.program.count()
    console.log(`🌍 国际项目数量: ${internationalPrograms}`)

    // 检查博客
    const blogs = await prisma.blog.count()
    console.log(`📝 博客数量: ${blogs}`)

    // 检查学员故事
    const testimonials = await prisma.testimonial.count()
    console.log(`💬 学员故事数量: ${testimonials}`)

    // 检查FAQ
    try {
      const faqs = await prisma.fAQ.count()
      console.log(`❓ FAQ数量: ${faqs}`)
    } catch (e) {
      console.log(`❓ FAQ数量: 表不存在或错误`)
    }

    // 检查视频
    const videos = await prisma.video.count()
    console.log(`🎥 视频数量: ${videos}`)

    // 检查媒体文件
    const media = await prisma.media.count()
    console.log(`📁 媒体文件数量: ${media}`)

    // 检查城市
    const cities = await prisma.city.count()
    console.log(`🏙️ 城市数量: ${cities}`)

    // 检查国家
    const countries = await prisma.country.count()
    console.log(`🌏 国家数量: ${countries}`)

    // 检查项目类型
    const programTypes = await prisma.programType.count()
    console.log(`📋 项目类型数量: ${programTypes}`)

    // 检查年级级别
    const gradeLevels = await prisma.gradeLevel.count()
    console.log(`🎓 年级级别数量: ${gradeLevels}`)

    // 检查主页项目精选
    try {
      const homepageShowcases = await prisma.homepageShowcase.count()
      console.log(`⭐ 主页项目精选数量: ${homepageShowcases}`)
    } catch (e) {
      console.log(`⭐ 主页项目精选数量: 表不存在或错误`)
    }

    console.log('\n✅ 备份数据检查完成！')

    // 显示一些具体数据
    if (chinaPrograms > 0) {
      console.log('\n📋 中国项目示例:')
      const sampleChina = await prisma.chinaProgram.findMany({
        take: 3,
        select: {
          id: true,
          title: true,
          city: true,
          duration: true
        }
      })
      sampleChina.forEach(program => {
        console.log(`  - ${program.title} (${program.city}, ${program.duration})`)
      })
    }

    if (internationalPrograms > 0) {
      console.log('\n🌍 国际项目示例:')
      const sampleIntl = await prisma.program.findMany({
        take: 3,
        select: {
          id: true,
          title: true,
          country: true,
          duration: true
        }
      })
      sampleIntl.forEach(program => {
        console.log(`  - ${program.title} (${program.country}, ${program.duration})`)
      })
    }

  } catch (error) {
    console.error('❌ 检查备份数据时出错:', error.message)
  } finally {
    await prisma.$disconnect()
  }
}

checkBackupData()
