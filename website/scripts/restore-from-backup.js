const { PrismaClient } = require('@prisma/client')
const fs = require('fs')
const path = require('path')

// 当前数据库
const currentPrisma = new PrismaClient()

async function restoreFromBackup() {
  console.log('🔄 开始从备份恢复数据...\n')

  try {
    // 1. 备份当前数据库
    console.log('1️⃣ 备份当前数据库...')
    const backupPath = `../backups/current-backup-before-restore-${new Date().toISOString().replace(/[:.]/g, '-')}.db`
    fs.copyFileSync('./prisma/dev.db', backupPath)
    console.log(`   ✅ 当前数据库已备份到: ${backupPath}`)

    // 2. 直接复制备份数据库文件
    console.log('\n2️⃣ 恢复备份数据库...')
    const backupDbPath = '../backups/full-backup-2025-06-02T07-38-19-140Z/database/dev.db'
    
    if (!fs.existsSync(backupDbPath)) {
      throw new Error(`备份数据库文件不存在: ${backupDbPath}`)
    }

    // 复制备份数据库到当前位置
    fs.copyFileSync(backupDbPath, './prisma/dev.db')
    console.log('   ✅ 备份数据库已恢复')

    // 3. 重新生成 Prisma 客户端以适应新数据库
    console.log('\n3️⃣ 重新生成 Prisma 客户端...')
    const { exec } = require('child_process')
    
    await new Promise((resolve, reject) => {
      exec('npx prisma generate', (error, stdout, stderr) => {
        if (error) {
          console.error('生成 Prisma 客户端时出错:', error)
          reject(error)
        } else {
          console.log('   ✅ Prisma 客户端已重新生成')
          resolve()
        }
      })
    })

    // 4. 验证恢复的数据
    console.log('\n4️⃣ 验证恢复的数据...')
    
    // 重新连接数据库
    await currentPrisma.$disconnect()
    const newPrisma = new PrismaClient()

    try {
      const users = await newPrisma.user.count()
      console.log(`   👥 用户数量: ${users}`)

      const chinaPrograms = await newPrisma.chinaProgram.count()
      console.log(`   🇨🇳 中国项目数量: ${chinaPrograms}`)

      const blogs = await newPrisma.blog.count()
      console.log(`   📝 博客数量: ${blogs}`)

      const testimonials = await newPrisma.testimonial.count()
      console.log(`   💬 学员故事数量: ${testimonials}`)

      const cities = await newPrisma.city.count()
      console.log(`   🏙️ 城市数量: ${cities}`)

      const countries = await newPrisma.country.count()
      console.log(`   🌏 国家数量: ${countries}`)

      const programTypes = await newPrisma.programType.count()
      console.log(`   📋 项目类型数量: ${programTypes}`)

      await newPrisma.$disconnect()

      console.log('\n✅ 数据恢复完成！')
      console.log('\n📋 恢复的数据包括:')
      console.log('   - 中国项目和相关数据')
      console.log('   - 博客文章')
      console.log('   - 学员故事')
      console.log('   - 城市和国家信息')
      console.log('   - 项目类型和年级级别')
      console.log('   - 媒体文件记录')

      console.log('\n⚠️  注意事项:')
      console.log('   - 请重启开发服务器以确保数据库连接正常')
      console.log('   - 如果遇到问题，可以从备份文件恢复')
      console.log(`   - 备份文件位置: ${backupPath}`)

    } catch (verifyError) {
      console.error('❌ 验证恢复数据时出错:', verifyError.message)
      console.log('\n🔄 尝试恢复原数据库...')
      fs.copyFileSync(backupPath, './prisma/dev.db')
      console.log('   ✅ 原数据库已恢复')
    }

  } catch (error) {
    console.error('❌ 恢复数据时出错:', error.message)
  } finally {
    await currentPrisma.$disconnect()
  }
}

restoreFromBackup()
