const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function checkAllData() {
  console.log('🔍 检查当前数据库内容...\n')

  let totalRecords = 0

  try {
    // 检查用户
    const users = await prisma.user.count()
    console.log(`👥 用户数量: ${users}`)
    totalRecords += users

    // 检查中国项目
    const chinaPrograms = await prisma.chinaProgram.count()
    console.log(`🇨🇳 中国项目数量: ${chinaPrograms}`)
    totalRecords += chinaPrograms

    // 检查国际项目
    const internationalPrograms = await prisma.program.count()
    console.log(`🌍 国际项目数量: ${internationalPrograms}`)
    totalRecords += internationalPrograms

    // 检查博客
    const blogs = await prisma.blog.count()
    console.log(`📝 博客数量: ${blogs}`)
    totalRecords += blogs

    // 检查学员故事
    const testimonials = await prisma.testimonial.count()
    console.log(`💬 学员故事数量: ${testimonials}`)
    totalRecords += testimonials

    // 检查FAQ
    try {
      const faqs = await prisma.fAQ.count()
      console.log(`❓ FAQ数量: ${faqs}`)
      totalRecords += faqs
    } catch (e) {
      console.log(`❓ FAQ数量: 表不存在`)
    }

    // 检查视频
    try {
      const videos = await prisma.video.count()
      console.log(`🎥 视频数量: ${videos}`)
      totalRecords += videos
    } catch (e) {
      console.log(`🎥 视频数量: 表不存在`)
    }

    // 检查媒体文件
    try {
      const media = await prisma.media.count()
      console.log(`📁 媒体文件数量: ${media}`)
      totalRecords += media
    } catch (e) {
      console.log(`📁 媒体文件数量: 表不存在`)
    }

    // 检查城市
    const cities = await prisma.city.count()
    console.log(`🏙️ 城市数量: ${cities}`)
    totalRecords += cities

    // 检查国家
    const countries = await prisma.country.count()
    console.log(`🌏 国家数量: ${countries}`)
    totalRecords += countries

    // 检查项目类型
    const programTypes = await prisma.programType.count()
    console.log(`📋 项目类型数量: ${programTypes}`)
    totalRecords += programTypes

    // 检查年级级别
    const gradeLevels = await prisma.gradeLevel.count()
    console.log(`🎓 年级级别数量: ${gradeLevels}`)
    totalRecords += gradeLevels

    // 检查主页项目精选
    try {
      const homepageShowcases = await prisma.homepageShowcase.count()
      console.log(`⭐ 主页项目精选数量: ${homepageShowcases}`)
      totalRecords += homepageShowcases
    } catch (e) {
      console.log(`⭐ 主页项目精选数量: 表不存在`)
    }

    // 检查合作伙伴
    try {
      const partners = await prisma.partner.count()
      console.log(`🤝 合作伙伴数量: ${partners}`)
      totalRecords += partners
    } catch (e) {
      console.log(`🤝 合作伙伴数量: 表不存在`)
    }

    // 检查Hero轮播
    try {
      const heroSlides = await prisma.heroSlide.count()
      console.log(`🎠 Hero轮播数量: ${heroSlides}`)
      totalRecords += heroSlides
    } catch (e) {
      console.log(`🎠 Hero轮播数量: 表不存在`)
    }

    console.log('\n✅ 数据检查完成！')
    console.log(`📊 总记录数: ${totalRecords}`)

  } catch (error) {
    console.error('❌ 检查数据时出错:', error)
  } finally {
    await prisma.$disconnect()
  }
}

checkAllData()
